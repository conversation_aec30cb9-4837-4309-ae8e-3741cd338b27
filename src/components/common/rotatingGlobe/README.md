# RotatingGlobe Component

A smooth, animated rotating globe component built with GSAP, inspired by Reflect.app's design.

## Features

- **Smooth GSAP Animation**: Continuous rotation with customizable speed
- **Hover Effects**: Pause rotation and scale on hover
- **Floating Animation**: Subtle vertical floating motion
- **Responsive Design**: Scales appropriately on different screen sizes
- **Performance Optimized**: Uses `will-change` and hardware acceleration
- **Visual Effects**: Gradient glow and depth shadows

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `globeImageUrl` | `string` | Required | URL of the globe image |
| `size` | `number` | `400` | Size of the globe in pixels |
| `rotationSpeed` | `number` | `20` | Duration of one full rotation in seconds |
| `pauseOnHover` | `boolean` | `true` | Whether to pause rotation on hover |
| `className` | `string` | `""` | Additional CSS classes |

## Usage

```tsx
import RotatingGlobe from "components/common/rotatingGlobe/RotatingGlobe";

<RotatingGlobe
  globeImageUrl="/images/globe.png"
  size={400}
  rotationSpeed={30}
  pauseOnHover={true}
  className="my-custom-class"
/>
```

## Integration

The component is currently integrated into:
- `src/components/home/<USER>/Banner.tsx` - Main banner section

## Performance Notes

- Uses GSAP for smooth 60fps animations
- Optimized for mobile with hardware acceleration
- Automatically cleans up animations on unmount
- Uses `useCallback` for event handlers to prevent unnecessary re-renders
