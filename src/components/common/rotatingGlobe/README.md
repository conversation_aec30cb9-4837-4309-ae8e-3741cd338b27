# RotatingGlobe Component

A realistic 3D rotating globe component built with Three.js and React Three Fiber, inspired by Reflect.app's design.

## Features

- **3D Rendering**: Real Three.js 3D globe with realistic lighting
- **Smooth Animation**: Continuous rotation with customizable speed
- **Hover Effects**: Pause rotation and scale on hover with cursor changes
- **Floating Animation**: Subtle vertical floating motion
- **Atmosphere Effect**: Glowing atmosphere layer around the globe
- **Enhanced Lighting**: Multiple light sources for realistic appearance
- **Responsive Design**: Scales appropriately on different screen sizes
- **Performance Optimized**: Hardware-accelerated WebGL rendering
- **Loading States**: Built-in loading fallback component

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `globeImageUrl` | `string` | Required | URL of the globe texture image |
| `size` | `number` | `400` | Size of the globe canvas in pixels |
| `rotationSpeed` | `number` | `8` | Speed multiplier for rotation (lower = faster) |
| `pauseOnHover` | `boolean` | `true` | Whether to pause rotation on hover |
| `enableControls` | `boolean` | `false` | Enable orbit controls for user interaction |
| `className` | `string` | `""` | Additional CSS classes |

## Usage

```tsx
import RotatingGlobe from "components/common/rotatingGlobe/RotatingGlobe";

<RotatingGlobe
  globeImageUrl="/images/globe.png"
  size={450}
  rotationSpeed={12}
  pauseOnHover={true}
  enableControls={false}
  className="my-custom-class"
/>
```

## Integration

The component is currently integrated into:
- `src/components/home/<USER>/Banner.tsx` - Main banner section

## Dependencies

- `three` - Core Three.js library
- `@react-three/fiber` - React renderer for Three.js
- `@react-three/drei` - Useful helpers for React Three Fiber

## Performance Notes

- Uses WebGL hardware acceleration for smooth 60fps rendering
- Optimized for mobile with proper fallbacks
- Automatically cleans up Three.js resources on unmount
- Suspense-based loading for better user experience
- Efficient texture loading and caching
- Responsive scaling for different screen sizes
