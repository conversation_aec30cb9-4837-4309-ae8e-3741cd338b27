@import "@styles/variables";
@import "@styles/mixins";

.rotating-globe {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .globe-image {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    transition: transform 0.3s ease;
    will-change: transform;
    position: relative;

    // Add a subtle glow effect
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.1) 50%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }

    // Add an inner shadow for depth
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 30%, transparent 30%, rgba(0, 0, 0, 0.1) 100%);
      border-radius: 50%;
      pointer-events: none;
    }
  }

  // Hover effects
  // &:hover {
  //   .globe-image {
  //     &::before {
  //       opacity: 1;
  //     }
  //   }
  // }

  // Responsive adjustments
  @media (max-width: $md) {
    .globe-image {
      // Ensure smooth performance on mobile
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }

  @media (max-width: $sm) {
    // Smaller size on mobile
    transform: scale(0.8);
  }
}
