@import "@styles/variables";
@import "@styles/mixins";

.rotating-globe {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible; // Changed from hidden to allow 3D effects

  // Three.js Canvas styling
  canvas {
    border-radius: 50%;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
    transition: filter 0.3s ease;

    &:hover {
      filter: drop-shadow(0 15px 40px rgba(59, 130, 246, 0.3));
    }
  }

  // Responsive adjustments
  @media (max-width: $md) {
    canvas {
      // Ensure smooth performance on mobile
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }

  @media (max-width: $sm) {
    // Smaller size on mobile
    transform: scale(0.8);
  }
}

// Loading fallback styles
.loading-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.1);
    border-top: 3px solid rgba(59, 130, 246, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
