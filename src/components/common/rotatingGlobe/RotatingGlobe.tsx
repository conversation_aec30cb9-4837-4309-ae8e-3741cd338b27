"use client";

import React, { Suspense, useRef, useState } from "react";
import { Canvas, useFrame, useLoader } from "@react-three/fiber";
import { OrbitControls, Sphere } from "@react-three/drei";
import * as THREE from "three";
import styles from "./RotatingGlobe.module.scss";

interface RotatingGlobeProps {
  globeImageUrl: string;
  size?: number;
  rotationSpeed?: number;
  pauseOnHover?: boolean;
  className?: string;
  enableControls?: boolean;
}

// Globe component that will be rendered inside the Canvas
const Globe: React.FC<{
  texture: string;
  rotationSpeed: number;
  pauseOnHover: boolean;
  onHover: (hovered: boolean) => void;
}> = ({ texture, rotationSpeed, pauseOnHover, onHover }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Load the texture
  const earthTexture = useLoader(THREE.TextureLoader, texture);

  // Animation loop
  useFrame((state, delta) => {
    if (meshRef.current && (!pauseOnHover || !hovered)) {
      meshRef.current.rotation.y += delta / rotationSpeed;
    }

    // Add subtle floating animation
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover(true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover(false);
    document.body.style.cursor = 'default';
  };

  return (
    <group>
      {/* Main Globe */}
      <Sphere
        ref={meshRef}
        args={[2, 64, 64]}
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        scale={hovered ? 1.05 : 1}
      >
        <meshStandardMaterial
          map={earthTexture}
          roughness={0.7}
          metalness={0.1}
          transparent={true}
          opacity={0.95}
        />
      </Sphere>

      {/* Atmosphere/Glow Effect */}
      <Sphere args={[2.1, 32, 32]} scale={hovered ? 1.05 : 1}>
        <meshBasicMaterial
          color="#4f9eff"
          transparent={true}
          opacity={0.1}
          side={THREE.BackSide}
        />
      </Sphere>
    </group>
  );
};

// Loading fallback component
const LoadingFallback: React.FC = () => (
  <div className={styles['loading-fallback']}>
    <div className={styles['loading-spinner']} />
  </div>
);

const RotatingGlobe: React.FC<RotatingGlobeProps> = ({
  globeImageUrl,
  size = 400,
  rotationSpeed = 8,
  pauseOnHover = true,
  className = "",
  enableControls = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`${styles['rotating-globe']} ${className}`}
      style={{ width: size, height: size }}
    >
      <Canvas
        camera={{ position: [0, 0, 5], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{ background: 'transparent' }}
      >
        {/* Enhanced Lighting setup */}
        <ambientLight intensity={0.3} />
        <directionalLight
          position={[5, 5, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-5, -5, -5]} intensity={0.4} color="#4f9eff" />
        <pointLight position={[2, -2, 2]} intensity={0.3} color="#ffffff" />

        <Suspense fallback={null}>
          <Globe
            texture={globeImageUrl}
            rotationSpeed={rotationSpeed}
            pauseOnHover={pauseOnHover}
            onHover={setIsHovered}
          />
        </Suspense>

        {enableControls && (
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={!isHovered}
            autoRotateSpeed={0.5}
          />
        )}
      </Canvas>
    </div>
  );
};

export default RotatingGlobe;