"use client";

import React, { useEffect, useRef, useCallback } from "react";
import { gsap } from "gsap";
import styles from "./RotatingGlobe.module.scss";

interface RotatingGlobeProps {
  globeImageUrl: string;
  size?: number;
  rotationSpeed?: number;
  pauseOnHover?: boolean;
  className?: string;
}

const RotatingGlobe: React.FC<RotatingGlobeProps> = ({
  globeImageUrl,
  size = 1400,
  rotationSpeed = 20,
  pauseOnHover = false,
  className = "",
}) => {
  const globeRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!globeRef.current) return;

    // Set initial transform origin to center
    gsap.set(globeRef.current, {
      transformOrigin: "center center",
    });

    // Create the rotation animation with smooth easing
    animationRef.current = gsap.to(globeRef.current, {
      rotation: 360,
      duration: rotationSpeed,
      ease: "none",
      repeat: -1,
    });

    // Add a subtle floating animation
    gsap.to(globeRef.current, {
      y: -10,
      duration: 3,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true,
    });

    // Cleanup function
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      gsap.killTweensOf(globeRef.current);
    };
  }, [rotationSpeed]);

  const handleMouseEnter = useCallback(() => {
    if (pauseOnHover && animationRef.current) {
      animationRef.current.pause();
    }

    // Add hover scale effect
    if (globeRef.current) {
      gsap.to(globeRef.current, {
        scale: 1.05,
        duration: 0.3,
        ease: "power2.out",
      });
    }
  }, [pauseOnHover]);

  const handleMouseLeave = useCallback(() => {
    if (pauseOnHover && animationRef.current) {
      animationRef.current.resume();
    }

    // Reset scale on mouse leave
    if (globeRef.current) {
      gsap.to(globeRef.current, {
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
      });
    }
  }, [pauseOnHover]);

  return (
    <div
      className={`${styles["rotating-globe"]} ${className}`}
      style={{ width: size, height: size }}
      // onMouseEnter={handleMouseEnter}
      // onMouseLeave={handleMouseLeave}
    >
      <div
        // ref={globeRef}
        className={styles["globe-image"]}
        style={{
          backgroundImage: `url(${globeImageUrl})`,
          width: `1400px`,
          height: `800px`,
        }}
      />
    </div>
  );
};

export default RotatingGlobe;
