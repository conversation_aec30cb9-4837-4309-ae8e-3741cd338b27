"use client";

import React from "react";
import styles from "./Banner.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import AnimatedTitleWithSubtitle from "components/common/animatedTitleWithSubtitle/AnimatedTitleWithSubtitle";
import RotatingGlobe from "components/common/rotatingGlobe/RotatingGlobe";
import { useTranslation } from "next-i18next";

const Banner = () => {
  const { t, i18n } = useTranslation();

  const backgroundImage = `${imageBaseUrl}/images/solutionHubBg.png`;
  const globe = `${imageBaseUrl}/images/globe.png`;
  const locale = i18n.language;

  const subTitles = [t("bannerSubTitle1"), t("bannerSubTitle2")];

  return (
    <>
      <style jsx>{`
        .banner {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: cover;
        }
      `}</style>
      <div className={`banner container ${styles["banner"]}`}>
        <div className={styles["banner__container"]}>
          <div className={styles["banner__content"]}>
            <AnimatedTitleWithSubtitle
              title={t("solutionHub")}
              subtitle={subTitles}
              locale={locale}
            />
          </div>
          <div className={styles["banner__globe"]}>
            <RotatingGlobe
              globeImageUrl={globe}
              size={450}
              rotationSpeed={12}
              pauseOnHover={true}
              enableControls={false}
              className={styles["rotating-globe"]}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Banner;
