@import "@styles/variables";
@import "@styles/mixins";

.cards-container {
  display: flex;
  // justify-content: space-between;
  padding: 0 0 64px 0;
  width: 100%;
  margin: -406px auto 0 auto;
  gap: 64px;

  &__card-contents {
    border-radius: 36px;
    border: 1px solid rgba(194, 184, 255, 0.3);
    padding: 40px;
    width: 50%;
    background: #13072d;
  }

  .brand-content {
    height: 785px;
  }

  &__chip {
    border-radius: 16px;
    background: #b800c4;
    display: flex;
    padding: 4px 8px 4px 4px;
    align-items: center;
    gap: 6px;
    align-self: stretch;
    width: fit-content;

    span {
      width: 12px;
      height: 12px;
      border-radius: 12px;
      background: #f985ff;
    }
    p {
      margin: 0;
      text-transform: uppercase;
      color: #fff;
      font-family: $default-font-family;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
    }
  }
  &__heading {
    font-family: $bricolage-font-family;
    font-optical-sizing: none;
    font-size: 40px;
    font-style: normal;
    font-weight: 800;
    line-height: 42px; /* 105% */
    letter-spacing: -1px;
    margin-bottom: 16px;
    margin-top: 16px;
    color: $lavender;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }
  &__description {
    margin: 0;
    color: rgba(167, 158, 218, 0.8);
    font-family: "Mona Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 25.2px;
    margin-bottom: 40px;
    
    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }
}
