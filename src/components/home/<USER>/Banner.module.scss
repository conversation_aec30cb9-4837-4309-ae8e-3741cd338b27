@import "@styles/variables";
@import "@styles/mixins";

.banner {
  height: 858px;
  width: 100%;
  margin-top: 140px;

  &__container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    @media (max-width: $lg) {
      flex-direction: column;
      justify-content: center;
      gap: 2rem;
    }
  }

  &__content {
    flex: 1;
    z-index: 2;

    @media (max-width: $lg) {
      text-align: center;
      order: 2;
    }
  }

  &__globe {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;

    @media (max-width: $lg) {
      order: 1;
    }

    @media (max-width: $md) {
      transform: scale(0.8);
    }

    @media (max-width: $sm) {
      transform: scale(0.6);
    }
  }

  .rotating-globe {
    // Additional styles for the rotating globe in banner context
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));

    @media (max-width: $lg) {
      margin-bottom: 1rem;
    }
  }
}
