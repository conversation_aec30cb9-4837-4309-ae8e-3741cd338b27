{"name": "solutions_hub_fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "start:local": "PORT=3000 node ./server-local.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "axios": "^1.9.0", "gsap": "^3.13.0", "i18next": "^25.1.2", "i18next-resources-to-backend": "^1.2.1", "next": "15.3.2", "next-i18n-router": "^5.5.1", "next-i18next": "^15.4.2", "next-intl": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-google-recaptcha": "^3.1.0", "react-i18next": "^15.5.1", "react-circle-flags": "^0.0.23", "react-phone-number-input": "^3.2.13"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "eslint": "^9", "eslint-config-next": "15.3.2", "sass": "^1.87.0", "typescript": "^5"}}